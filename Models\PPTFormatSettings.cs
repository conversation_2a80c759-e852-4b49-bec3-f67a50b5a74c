using System;
using System.Collections.Generic;

namespace PPTPiliangChuli.Models
{
    /// <summary>
    /// PPT格式设置模型类 - 定义PPT全局格式设置的配置参数
    /// 包含段落格式、字体格式、主题设置、母版设置、样式设置等完整配置结构
    /// 符合Aspose.Slides API规范，支持批量处理和高级格式设置选项
    /// </summary>
    public class PPTFormatSettings
    {
        /// <summary>
        /// 是否启用PPT格式设置功能
        /// </summary>
        public bool EnablePPTFormatSettings { get; set; } = false;

        /// <summary>
        /// 是否启用段落格式设置
        /// </summary>
        public bool EnableParagraphFormat { get; set; } = false;

        /// <summary>
        /// 是否启用对齐方式设置
        /// </summary>
        public bool EnableAlignment { get; set; } = false;

        /// <summary>
        /// 对齐方式 (Left, Center, Right, Justify, Distributed)
        /// </summary>
        public string Alignment { get; set; } = "Left";

        /// <summary>
        /// 是否启用缩进设置
        /// </summary>
        public bool EnableIndent { get; set; } = false;

        /// <summary>
        /// 文本之前缩进（厘米）
        /// </summary>
        public float IndentBefore { get; set; } = 0.0f;

        /// <summary>
        /// 特殊缩进类型（无、首行缩进、悬挂缩进）
        /// </summary>
        public string SpecialIndentType { get; set; } = "无";

        /// <summary>
        /// 特殊缩进值（字符）
        /// </summary>
        public float SpecialIndentValue { get; set; } = 2.0f;

        /// <summary>
        /// 是否启用间距设置
        /// </summary>
        public bool EnableSpacing { get; set; } = false;

        /// <summary>
        /// 段前间距（磅）
        /// </summary>
        public float SpacingBefore { get; set; } = 0.0f;

        /// <summary>
        /// 段后间距（磅）
        /// </summary>
        public float SpacingAfter { get; set; } = 0.0f;

        /// <summary>
        /// 行距类型（单倍行距、1.5倍行距、2倍行距、多倍行距、固定值）
        /// </summary>
        public string LineSpacingType { get; set; } = "单倍行距";

        /// <summary>
        /// 行距值
        /// </summary>
        public float LineSpacingValue { get; set; } = 1.0f;

        /// <summary>
        /// 是否启用段落选项
        /// </summary>
        public bool EnableOptions { get; set; } = false;

        /// <summary>
        /// 按中文习惯控制中文首尾字符
        /// </summary>
        public bool ChineseControl { get; set; } = true;

        /// <summary>
        /// 允许西文在单词中间换行
        /// </summary>
        public bool WordWrap { get; set; } = false;

        /// <summary>
        /// 允许标点移除边界
        /// </summary>
        public bool PunctuationBoundary { get; set; } = false;

        /// <summary>
        /// 是否启用文本对齐设置
        /// </summary>
        public bool EnableTextAlign { get; set; } = false;

        /// <summary>
        /// 文本对齐类型（自动、居中、基线、底部）
        /// </summary>
        public string TextAlignmentType { get; set; } = "自动";

        /// <summary>
        /// 是否启用字体格式设置
        /// </summary>
        public bool EnableFontFormat { get; set; } = false;

        /// <summary>
        /// 是否启用字体选择设置
        /// </summary>
        public bool EnableFontSelection { get; set; } = false;

        /// <summary>
        /// 中文字体
        /// </summary>
        public string ChineseFont { get; set; } = "宋体";

        /// <summary>
        /// 英文字体
        /// </summary>
        public string EnglishFont { get; set; } = "Arial";

        /// <summary>
        /// 是否启用字体样式设置
        /// </summary>
        public bool EnableFontStyle { get; set; } = false;

        /// <summary>
        /// 字体样式（常规、加粗、倾斜、倾斜加粗）
        /// </summary>
        public string FontStyle { get; set; } = "常规";

        /// <summary>
        /// 字体大小
        /// </summary>
        public float FontSize { get; set; } = 12.0f;

        /// <summary>
        /// 字体颜色（十六进制）
        /// </summary>
        public string FontColor { get; set; } = "#000000";

        /// <summary>
        /// 是否启用字体效果设置
        /// </summary>
        public bool EnableFontEffects { get; set; } = false;

        /// <summary>
        /// 下划线类型（无、单下划线、双下划线、波浪线等）
        /// </summary>
        public string UnderlineType { get; set; } = "无";

        /// <summary>
        /// 下划线颜色（十六进制）
        /// </summary>
        public string UnderlineColor { get; set; } = "#000000";

        /// <summary>
        /// 删除线
        /// </summary>
        public bool Strikethrough { get; set; } = false;

        /// <summary>
        /// 双删除线
        /// </summary>
        public bool DoubleStrikethrough { get; set; } = false;

        /// <summary>
        /// 上标
        /// </summary>
        public bool Superscript { get; set; } = false;

        /// <summary>
        /// 下标
        /// </summary>
        public bool Subscript { get; set; } = false;

        /// <summary>
        /// 是否启用主题设置
        /// </summary>
        public bool EnableTheme { get; set; } = false;

        /// <summary>
        /// 是否启用内置主题设置
        /// </summary>
        public bool EnableBuiltInTheme { get; set; } = false;

        /// <summary>
        /// 选中的主题名称
        /// </summary>
        public string SelectedTheme { get; set; } = "Office 主题";

        /// <summary>
        /// 是否启用颜色方案设置
        /// </summary>
        public bool EnableColorScheme { get; set; } = false;

        /// <summary>
        /// 选中的颜色方案
        /// </summary>
        public string SelectedColorScheme { get; set; } = "Office 颜色方案";

        /// <summary>
        /// 是否启用字体方案设置
        /// </summary>
        public bool EnableFontScheme { get; set; } = false;

        /// <summary>
        /// 选中的字体方案
        /// </summary>
        public string SelectedFontScheme { get; set; } = "Office 字体方案";

        /// <summary>
        /// 是否启用主题效果设置
        /// </summary>
        public bool EnableThemeEffects { get; set; } = false;

        /// <summary>
        /// 效果强度（0-100）
        /// </summary>
        public int EffectIntensity { get; set; } = 50;

        /// <summary>
        /// 是否启用主题背景设置
        /// </summary>
        public bool EnableThemeBackground { get; set; } = false;

        /// <summary>
        /// 背景类型（纯色背景、渐变背景、图片背景）
        /// </summary>
        public string BackgroundType { get; set; } = "纯色背景";

        /// <summary>
        /// 背景颜色（十六进制）
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 透明度（0-100）
        /// </summary>
        public int Transparency { get; set; } = 0;

        /// <summary>
        /// 是否启用母版设置
        /// </summary>
        public bool EnableMaster { get; set; } = false;

        /// <summary>
        /// 幻灯片母版文件路径
        /// </summary>
        public string SlideMasterFile { get; set; } = "";

        /// <summary>
        /// 标题母版文件路径
        /// </summary>
        public string TitleMasterFile { get; set; } = "";

        /// <summary>
        /// 备注母版文件路径
        /// </summary>
        public string NotesMasterFile { get; set; } = "";

        /// <summary>
        /// 讲义母版文件路径
        /// </summary>
        public string HandoutMasterFile { get; set; } = "";

        /// <summary>
        /// 是否启用母版应用
        /// </summary>
        public bool EnableMasterApplication { get; set; } = false;

        /// <summary>
        /// 保留内容
        /// </summary>
        public bool PreserveContent { get; set; } = true;

        /// <summary>
        /// 应用颜色方案
        /// </summary>
        public bool ApplyColorScheme { get; set; } = true;

        /// <summary>
        /// 应用字体方案
        /// </summary>
        public bool ApplyFontScheme { get; set; } = true;

        /// <summary>
        /// 是否启用母版背景设置
        /// </summary>
        public bool EnableMasterBackground { get; set; } = false;

        /// <summary>
        /// 母版背景类型（纯色背景、渐变背景、图片背景）
        /// </summary>
        public string MasterBackgroundType { get; set; } = "纯色背景";

        /// <summary>
        /// 母版背景颜色（十六进制）
        /// </summary>
        public string MasterBackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 母版背景透明度（0-100）
        /// </summary>
        public int MasterBackgroundTransparency { get; set; } = 0;

        /// <summary>
        /// 是否启用母版文本样式设置
        /// </summary>
        public bool EnableMasterTextStyle { get; set; } = false;

        /// <summary>
        /// 母版标题字体
        /// </summary>
        public string MasterTitleFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 母版标题字体大小
        /// </summary>
        public int MasterTitleSize { get; set; } = 24;

        /// <summary>
        /// 母版标题颜色（十六进制）
        /// </summary>
        public string MasterTitleColor { get; set; } = "#000000";

        /// <summary>
        /// 母版正文字体
        /// </summary>
        public string MasterBodyFont { get; set; } = "微软雅黑";

        /// <summary>
        /// 母版正文字体大小
        /// </summary>
        public int MasterBodySize { get; set; } = 14;

        /// <summary>
        /// 母版正文颜色（十六进制）
        /// </summary>
        public string MasterBodyColor { get; set; } = "#000000";

        /// <summary>
        /// 是否启用样式设置
        /// </summary>
        public bool EnableStyle { get; set; } = false;

        /// <summary>
        /// 是否启用形状样式设置
        /// </summary>
        public bool EnableShapeStyle { get; set; } = false;

        /// <summary>
        /// 是否启用文本样式设置
        /// </summary>
        public bool EnableTextStyle { get; set; } = false;

        /// <summary>
        /// 是否启用表格样式设置
        /// </summary>
        public bool EnableTableStyle { get; set; } = false;

        /// <summary>
        /// 是否启用图表样式设置
        /// </summary>
        public bool EnableChartStyle { get; set; } = false;

        /// <summary>
        /// 是否启用SmartArt样式设置
        /// </summary>
        public bool EnableSmartArtStyle { get; set; } = false;

        /// <summary>
        /// 是否启用多媒体样式设置
        /// </summary>
        public bool EnableMultimediaStyle { get; set; } = false;
    }
}
